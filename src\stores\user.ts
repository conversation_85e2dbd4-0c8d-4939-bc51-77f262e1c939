import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getMemberInfo, type UserMemberInfo } from '@/api/user'
import { showToast } from '@/utils/toast'

// 定义用户信息接口（兼容旧版本）
interface UserInfo {
  userId?: string
  id?: string
  username?: string
  nickname?: string
  avatar?: string
  phone?: string
  level?: string
  balance?: number
}

// 定义用户状态
export const useUserStore = defineStore('user', () => {
  // 用户信息（新版API数据）
  const memberInfo = ref<UserMemberInfo | null>(null)
  
  // 用户信息（兼容旧版本）
  const userInfo = ref<UserInfo | null>(null)

  // 登录状态
  const isLoggedIn = ref(false)

  // 设置用户信息
  function setUserInfo(info: UserInfo) {
    userInfo.value = info
    isLoggedIn.value = true
  }

  // 设置用户详细信息
  function setMemberInfo(info: UserMemberInfo) {
    memberInfo.value = info
    // 同时更新兼容的用户信息
    userInfo.value = {
      userId: info.id.toString(),
      id: info.id.toString(),
      username: info.username,
      nickname: info.nickname || info.username,
      avatar: info.avatar,
      phone: info.mobile,
      level: 'VIP', // 默认等级
      balance: info.jifen * 0.1 // 积分转换为余额，按照1积分=0.1元
    }
    isLoggedIn.value = true
  }

  // 获取用户信息
  async function fetchMemberInfo() {
    try {
      const response = await getMemberInfo()
      
      if (response.code === 1 && response.data) {
        setMemberInfo(response.data)
        return response.data
      } else {
        console.error('获取用户信息失败:', response.msg)
        showToast(response.msg || '获取用户信息失败')
        return null
      }
    } catch (error) {
      console.error('获取用户信息异常:', error)
      showToast('获取用户信息失败')
      return null
    }
  }

  // 初始化用户状态（从localStorage恢复）
  function initUserState() {
    const token = localStorage.getItem('token')
    if (token) {
      // 如果有token，设置为已登录状态
      // 注意：这里只是设置基本的登录状态，具体的用户信息需要通过API获取
      isLoggedIn.value = true
      console.log('从localStorage恢复登录状态')
    }
  }

  // 清除用户信息
  function clearUserInfo() {
    userInfo.value = null
    memberInfo.value = null
    isLoggedIn.value = false
    localStorage.removeItem('token')
  }

  return {
    userInfo,
    memberInfo,
    isLoggedIn,
    setUserInfo,
    setMemberInfo,
    fetchMemberInfo,
    clearUserInfo,
    initUserState
  }
}) 