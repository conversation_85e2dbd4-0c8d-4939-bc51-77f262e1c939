<script setup lang="ts">
// App.vue根组件
import TabBar from '@/components/TabBar.vue';
import { useRoute } from 'vue-router';
import { computed, onMounted } from 'vue';
import { useUserStore } from '@/stores/user';

const route = useRoute();
const userStore = useUserStore();

// 判断当前页面是否需要显示底部标签栏
const showTabBar = computed(() => {
  const { path } = route;
  // 不显示底部标签栏的页面：登录、注册、找回密码、任务详情、账号库相关页面、任务提交页面、绑定微信页面
  const hideTabBarPages = ['/login', '/register', '/forgot-password', '/task-detail', '/accounts', '/submit-task', '/bind-wechat'];
  return !hideTabBarPages.some(page => path.includes(page));
});

// 判断当前页面是否需要缓存
const shouldKeepAlive = computed(() => {
  // 任务详情页面不使用缓存，确保每次都是新的实例
  if (route.path.includes('/task-detail')) {
    return false;
  }
  return route.meta.keepAlive !== false;
});

// 应用启动时初始化用户状态
onMounted(() => {
  userStore.initUserState();
});
</script>

<template>
  <div class="app-container">
    <router-view v-slot="{ Component }">
      <keep-alive v-if="shouldKeepAlive">
        <component :is="Component" />
      </keep-alive>
      <component v-else :is="Component" />
    </router-view>

    <!-- 底部标签栏 -->
    <tab-bar v-if="showTabBar" />
  </div>
</template>

<style scoped>
.app-container {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}
</style> 