import axios from 'axios'

// 根据环境判断API基础地址
const baseURL = process.env.NODE_ENV === 'development' ? '/api' : 'http://h5.zgjcks.com/api'

// 创建axios实例
const request = axios.create({
  baseURL,
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 添加token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const res = response.data

    // 调试日志（可以在生产环境中移除）
    if (process.env.NODE_ENV === 'development') {
      console.log('API响应数据:', {
        url: response.config.url,
        data: res,
        code: res?.code,
        msg: res?.msg
      })
    }

    // 处理API响应格式 {code: number, msg: string, data: any, time: string}
    if (res && typeof res === 'object' && 'code' in res) {
      // 特殊处理未登录情况
      if (res.code === 0 && res.msg === "未登录") {
        // 清除token并跳转到登录页面
        localStorage.removeItem('token')
        window.location.href = '/#/login'
        // 抛出特殊错误，不显示toast
        return Promise.reject(new Error('未登录'))
      }
      
      // 所有接口统一：code为1表示成功，code为0表示失败
      if (res.code === 1) {
        return res
      } else {
        // API返回错误信息，不在拦截器中显示toast，交给业务代码处理
        const originalMsg = res.msg
        const trimmedMsg = originalMsg && originalMsg.trim()
        const errorMsg = trimmedMsg || '请求失败'

        // 调试日志
        if (process.env.NODE_ENV === 'development') {
          console.log('API错误信息:', { originalMsg, finalErrorMsg: errorMsg })
        }

        // 创建包含完整响应信息的错误对象
        const error = new Error(errorMsg)
        ;(error as any).response = res
        ;(error as any).code = res.code
        return Promise.reject(error)
      }
    }
    
    return res
  },
  error => {
    console.error('API请求错误:', error)
    
    if (error.response) {
      // 服务器响应了错误状态码
      if (error.response.status === 401) {
        // 401错误自动处理，但不显示toast，避免与业务代码冲突
        localStorage.removeItem('token')
        window.location.href = '/#/login'
      } else {
        // 其他HTTP错误不显示toast，交给业务代码处理
        console.log('HTTP错误:', error.response.status, error.response.data)
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应，交给业务代码处理
      console.log('网络连接失败:', error.request)
    } else {
      // 发生了触发请求错误的问题，不显示toast，交给业务代码处理
      console.log('请求错误:', error.message)
    }
    
    return Promise.reject(error)
  }
)

export default request 